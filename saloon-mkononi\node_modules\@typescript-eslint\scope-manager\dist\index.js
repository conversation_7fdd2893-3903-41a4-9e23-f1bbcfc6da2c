"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ScopeManager = exports.Visitor = exports.Reference = exports.PatternVisitor = exports.analyze = void 0;
var analyze_1 = require("./analyze");
Object.defineProperty(exports, "analyze", { enumerable: true, get: function () { return analyze_1.analyze; } });
__exportStar(require("./definition"), exports);
var PatternVisitor_1 = require("./referencer/PatternVisitor");
Object.defineProperty(exports, "PatternVisitor", { enumerable: true, get: function () { return PatternVisitor_1.PatternVisitor; } });
var Reference_1 = require("./referencer/Reference");
Object.defineProperty(exports, "Reference", { enumerable: true, get: function () { return Reference_1.Reference; } });
var Visitor_1 = require("./referencer/Visitor");
Object.defineProperty(exports, "Visitor", { enumerable: true, get: function () { return Visitor_1.Visitor; } });
__exportStar(require("./scope"), exports);
var ScopeManager_1 = require("./ScopeManager");
Object.defineProperty(exports, "ScopeManager", { enumerable: true, get: function () { return ScopeManager_1.ScopeManager; } });
__exportStar(require("./variable"), exports);

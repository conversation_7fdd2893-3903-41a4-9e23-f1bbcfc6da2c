{"version": 3, "names": ["_superPropBase", "require", "_get", "Reflect", "get", "exports", "default", "bind", "target", "property", "receiver", "base", "superPropBase", "desc", "Object", "getOwnPropertyDescriptor", "call", "arguments", "length", "value", "apply"], "sources": ["../../src/helpers/get.ts"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nimport superPropBase from \"./superPropBase.ts\";\n\n// https://tc39.es/ecma262/multipage/reflection.html#sec-reflect.get\n//\n//  28.1ak.5 Reflect.get ( target, propertyKey [ , receiver ] )\nexport default function _get<T extends object, P extends PropertyKey>(\n  target: T,\n  property: P,\n  receiver?: unknown,\n): P extends keyof T ? T[P] : any;\nexport default function _get(): any {\n  if (typeof Reflect !== \"undefined\" && Reflect.get) {\n    // need a bind because https://github.com/babel/babel/issues/14527\n    // @ts-expect-error function reassign\n    _get = Reflect.get.bind(/* undefined */);\n  } else {\n    // @ts-expect-error function reassign\n    _get = function _get(target, property, receiver) {\n      var base = superPropBase(target, property);\n\n      if (!base) return;\n\n      var desc = Object.getOwnPropertyDescriptor(base, property)!;\n      if (desc.get) {\n        // STEP 3. If receiver is not present, then set receiver to target.\n        return desc.get.call(arguments.length < 3 ? target : receiver);\n      }\n\n      return desc.value;\n    };\n  }\n\n  return _get.apply(null, arguments as any as Parameters<typeof Reflect.get>);\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,cAAA,GAAAC,OAAA;AAUe,SAASC,IAAIA,CAAA,EAAQ;EAClC,IAAI,OAAOC,OAAO,KAAK,WAAW,IAAIA,OAAO,CAACC,GAAG,EAAE;IAGjDC,OAAA,CAAAC,OAAA,GAAAJ,IAAI,GAAGC,OAAO,CAACC,GAAG,CAACG,IAAI,CAAgB,CAAC;EAC1C,CAAC,MAAM;IAELF,OAAA,CAAAC,OAAA,GAAAJ,IAAI,GAAG,SAASA,IAAIA,CAACM,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;MAC/C,IAAIC,IAAI,GAAG,IAAAC,sBAAa,EAACJ,MAAM,EAAEC,QAAQ,CAAC;MAE1C,IAAI,CAACE,IAAI,EAAE;MAEX,IAAIE,IAAI,GAAGC,MAAM,CAACC,wBAAwB,CAACJ,IAAI,EAAEF,QAAQ,CAAE;MAC3D,IAAII,IAAI,CAACT,GAAG,EAAE;QAEZ,OAAOS,IAAI,CAACT,GAAG,CAACY,IAAI,CAACC,SAAS,CAACC,MAAM,GAAG,CAAC,GAAGV,MAAM,GAAGE,QAAQ,CAAC;MAChE;MAEA,OAAOG,IAAI,CAACM,KAAK;IACnB,CAAC;EACH;EAEA,OAAOjB,IAAI,CAACkB,KAAK,CAAC,IAAI,EAAEH,SAAkD,CAAC;AAC7E", "ignoreList": []}